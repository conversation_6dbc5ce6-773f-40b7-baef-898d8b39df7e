<nav class="navbar navbar-expand-lg navbar-light bg-primary fixed-top py-1 asap-sans-serif">
    <div class="container">
        <div class="img-box">
            <a class="navbar-brand" href="/"><img src="{{ asset($general_settings['full_logo']) }}" class="img-box"></a>
        </div>
        <button class="navbar-toggler ms-2 shadow-none" type="button" data-bs-toggle="collapse"
            data-bs-target="#navigation" aria-controls="navigation" aria-expanded="false"
            aria-label="Toggle navigation">
            <span class="navbar-toggler-icon mt-2">
                <span class="navbar-toggler-bar bar1"></span>
                <span class="navbar-toggler-bar bar2"></span>
                <span class="navbar-toggler-bar bar3"></span>
            </span>
        </button>

        <div class="navbar-collapse w-100 py-lg-0 ms-lg-4 ps-lg-5 collapse pb-2 pt-3" id="navigation">
            <ul class="justify-content-end navbar-nav navbar-nav-hover w-100">
                <li class="nav-item mx-2">
                    <a class="nav-link d-flex justify-content-between align-items-center {{ Request::is('/') ? 'active text-dark fw-bold ' : 'text-gray fw-bold' }} cursor-pointer ps-2"
                        href="{{url('/#home')}}">{{ get_label('home', 'Home') }}</a>
                </li>
                <li class="nav-item mx-2">
                    <a class="nav-link d-flex justify-content-between align-items-center {{ Request::is('about-us') ? 'active text-dark fw-bold ' : 'text-gray fw-bold' }} cursor-pointer ps-2"
                        href="{{url('/#about-section')}}">{{ get_label('about_us', 'About Us') }}</a>
                </li>
                <li class="nav-item mx-2">
                    <a class="nav-link d-flex justify-content-between align-items-center {{ Request::is('pricing') ? 'active text-dark fw-bold ' : 'text-gray fw-bold' }} cursor-pointer ps-2"
                        href="{{url('/#pricing-plans-section')}}">{{ get_label('pricing_plans', 'Pricing Plans') }}</a>
                </li>
                <li class="nav-item mx-2">
                    <a class="nav-link d-flex justify-content-between align-items-center {{ Request::is('features') ? 'active text-dark fw-bold ' : 'text-gray fw-bold' }} cursor-pointer ps-2"
                        href="{{url('/#features-section')}}">
                        {{ get_label('features', 'Features') }}
                    </a>
                </li>
                <li class="nav-item mx-2">
                    <a class="nav-link d-flex justify-content-between align-items-center {{ Request::is('contact-us') ? 'active text-dark fw-bold ' : 'text-gray fw-bold' }} cursor-pointer ps-2"
                        href="{{url('/#contact-us-section')}}">{{ get_label('contact_us', 'Contact Us') }}</a>
                </li>

                <li class="nav-item dropdown dropdown-hover mx-2">
                    <a class="nav-link justify-content-between align-items-center cursor-pointer ps-2"
                        id="dropdownMenuPages1" data-bs-toggle="dropdown" aria-expanded="false" href="#">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="language-icon">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM11 19.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" fill="currentColor"/>
                        </svg>
                        <img src="https://demos.creative-tim.com/soft-ui-design-system/assets/img/down-arrow-dark.svg"
                            alt="down-arrow" class="arrow ms-1">
                    </a>
                    <ul class="dropdown-menu dropdown-menu-animation mt-lg-3 border-radius-lg w-50 mt-0 p-3"
                        aria-labelledby="dropdownMenuDocs">
                        @foreach ($languages as $language)
                            <li class="dropdown-item  rounded">
                                <a class="d-block w-100 text-decoration-none {{ $language->code == app()->getLocale() ? ' text-dark fw-bold' : 'text-dark' }}"
                                    href="{{ route('languages.switch', ['code' => $language->code]) }}">
                                    {{ $language->name }}
                                </a>
                            </li>
                        @endforeach
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                    </ul>
                </li>



            </ul>

            <div class="col-xs-12 col-sm-12 col-md-3 col-lg-3 col-xl-2">
                @if (auth()->check())
                    @if (auth()->user()->hasRole('superadmin'))
                        <a href="{{ route('superadmin.panel') }}"
                            class="btn btn-outline-login btn-sm btn-round mt-md-0 mb-0 me-1 mt-2">{{ get_label('dashboard', 'Dashboard') }}</a>
                    @else
                        <a href="{{ route('home.index') }}"
                            class="btn btn-outline-login btn-sm btn-round mt-md-0 mb-0 me-1 mt-2">{{ get_label('dashboard', 'Dashboard') }}</a>
                    @endif
                @else
                    <a href="{{ route('login') }}"
                        class="btn btn-outline-login btn-sm btn-round mt-md-0 mb-0 me-1 mt-2">{{ get_label('login', 'Login') }}</a>
                @endif
            </div>


        </div>
    </div>
</nav>